import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../../utils/app_config.dart';
import '../../models/auth_models.dart';
import '../storage_service.dart';


/// خدمة API الأساسية للتعامل مع ASP.NET Core API
class ApiService {
  static final ApiService _instance = ApiService._internal();
  factory ApiService() => _instance;
  ApiService._internal();

  StorageService? _storageService;
  
  String? _accessToken;
  String? _refreshToken;
  DateTime? _tokenExpiresAt;

  /// الحصول على رمز الوصول الحالي
  String? get accessToken => _accessToken;

  /// تهيئة الخدمة وتحميل الرموز المحفوظة
  Future<void> initialize() async {
    try {
      _storageService = StorageService.instance;
    } catch (e) {
      debugPrint('تحذير: لم يتم تهيئة StorageService: $e');
    }
    await _loadTokensFromStorage();
  }

  /// تحميل الرموز من التخزين المحلي
  Future<void> _loadTokensFromStorage() async {
    if (_storageService == null) {
      debugPrint('StorageService غير متاح');
      return;
    }

    try {
      final sessionData = await _storageService!.getSessionData();
      if (sessionData != null && !sessionData.isExpired) {
        _accessToken = sessionData.accessToken;
        _refreshToken = sessionData.refreshToken;
        _tokenExpiresAt = sessionData.expiresAt;
        debugPrint('تم تحميل الرموز من التخزين المحلي');
      } else {
        debugPrint('لا توجد جلسة صالحة في التخزين المحلي');
        await clearTokens();
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الرموز: $e');
      await clearTokens();
    }
  }

  /// حفظ الرموز في التخزين المحلي
  Future<void> _saveTokensToStorage(AuthResponse authResponse) async {
    if (authResponse.isValid && _storageService != null) {
      _accessToken = authResponse.accessToken;
      _refreshToken = authResponse.refreshToken;
      _tokenExpiresAt = authResponse.expiresAt;

      final sessionData = SessionData(
        accessToken: authResponse.accessToken!,
        refreshToken: authResponse.refreshToken,
        expiresAt: authResponse.expiresAt!,
        user: authResponse.user!,
        savedAt: DateTime.now(),
      );

      await _storageService!.saveSessionData(sessionData);
      debugPrint('تم حفظ الرموز في التخزين المحلي');
    }
  }

  /// مسح الرموز
  Future<void> clearTokens() async {
    _accessToken = null;
    _refreshToken = null;
    _tokenExpiresAt = null;
    if (_storageService != null) {
      await _storageService!.clearSessionData();
    }
    debugPrint('تم مسح الرموز');
  }

  /// التحقق من صحة الرمز وتحديثه إذا لزم الأمر
  Future<bool> _ensureValidToken() async {
    if (_accessToken == null) {
      debugPrint('لا يوجد رمز وصول');
      return false;
    }

    // التحقق من انتهاء صلاحية الرمز
    if (_tokenExpiresAt != null && DateTime.now().isAfter(_tokenExpiresAt!)) {
      debugPrint('انتهت صلاحية الرمز، محاولة التحديث...');
      return await _refreshAccessToken();
    }

    // التحقق من الحاجة لتحديث الرمز (قبل انتهاء الصلاحية بـ 5 دقائق)
    if (_tokenExpiresAt != null) {
      final refreshTime = _tokenExpiresAt!.subtract(const Duration(minutes: 5));
      if (DateTime.now().isAfter(refreshTime)) {
        debugPrint('الرمز يحتاج للتحديث قريباً، محاولة التحديث...');
        await _refreshAccessToken(); // لا نرجع false إذا فشل التحديث
      }
    }

    return true;
  }

  /// تحديث رمز الوصول
  Future<bool> _refreshAccessToken() async {
    if (_refreshToken == null) {
      debugPrint('لا يوجد رمز تحديث');
      return false;
    }

    try {
      final request = RefreshTokenRequest(refreshToken: _refreshToken!);
      final response = await _makeRequest(
        'POST',
        '/api/Auth/refresh-token',
        body: request.toJson(),
        requireAuth: false, // لا نحتاج مصادقة لتحديث الرمز
      );

      if (response.statusCode == 200) {
        final authResponse = AuthResponse.fromJson(jsonDecode(response.body));
        if (authResponse.isValid) {
          await _saveTokensToStorage(authResponse);
          debugPrint('تم تحديث الرمز بنجاح');
          return true;
        }
      }

      debugPrint('فشل في تحديث الرمز: ${response.statusCode}');
      await clearTokens();
      return false;
    } catch (e) {
      debugPrint('خطأ في تحديث الرمز: $e');
      await clearTokens();
      return false;
    }
  }

  /// إنشاء طلب HTTP
  Future<http.Response> _makeRequest(
    String method,
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    Map<String, dynamic>? queryParams,
    bool requireAuth = true,
  }) async {
    // التحقق من الرمز إذا كان مطلوباً
    if (requireAuth && !await _ensureValidToken()) {
      throw ApiException('غير مصرح - يرجى تسجيل الدخول مرة أخرى', 401);
    }

    // إنشاء URI مع query parameters إذا كانت موجودة
    Uri uri;
    if (queryParams != null && queryParams.isNotEmpty) {
      // تحويل Map<String, dynamic> إلى Map<String, String>
      final stringParams = queryParams.map((key, value) => MapEntry(key, value.toString()));
      uri = Uri.parse('${AppConfig.apiUrl}$endpoint').replace(queryParameters: stringParams);
    } else {
      uri = Uri.parse('${AppConfig.apiUrl}$endpoint');
    }
    
    final requestHeaders = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...?headers,
    };

    // إضافة رمز المصادقة إذا كان متوفراً ومطلوباً
    if (requireAuth && _accessToken != null) {
      requestHeaders['Authorization'] = 'Bearer $_accessToken';
    }

    http.Response response;
    final bodyJson = body != null ? jsonEncode(body) : null;

    try {
      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(uri, headers: requestHeaders);
          break;
        case 'POST':
          response = await http.post(uri, headers: requestHeaders, body: bodyJson);
          break;
        case 'PUT':
          response = await http.put(uri, headers: requestHeaders, body: bodyJson);
          break;
        case 'PATCH':
          response = await http.patch(uri, headers: requestHeaders, body: bodyJson);
          break;
        case 'DELETE':
          response = await http.delete(uri, headers: requestHeaders);
          break;
        default:
          throw ApiException('طريقة HTTP غير مدعومة: $method', 400);
      }

      debugPrint('API Request: $method $endpoint - Status: ${response.statusCode}');

      // معالجة خاصة لحالة 307 Redirect
      if (response.statusCode == 307) {
        debugPrint('تم اكتشاف إعادة توجيه 307. محاولة إعادة الطلب...');

        // محاولة الحصول على عنوان إعادة التوجيه من headers
        final location = response.headers['location'];
        if (location != null) {
          debugPrint('عنوان إعادة التوجيه: $location');

          // إعادة إنشاء الطلب مع العنوان الجديد
          final newUri = Uri.parse(location);

          switch (method.toUpperCase()) {
            case 'GET':
              response = await http.get(newUri, headers: requestHeaders);
              break;
            case 'POST':
              response = await http.post(newUri, headers: requestHeaders, body: bodyJson);
              break;
            case 'PUT':
              response = await http.put(newUri, headers: requestHeaders, body: bodyJson);
              break;
            case 'PATCH':
              response = await http.patch(newUri, headers: requestHeaders, body: bodyJson);
              break;
            case 'DELETE':
              response = await http.delete(newUri, headers: requestHeaders);
              break;
          }

          debugPrint('API Request (بعد إعادة التوجيه): $method $location - Status: ${response.statusCode}');
        }
      }

      return response;
    } on SocketException {
      throw ApiException('لا يمكن الاتصال بالخادم. تحقق من اتصال الإنترنت.', 0);
    } on HttpException catch (e) {
      throw ApiException('خطأ في الشبكة: ${e.message}', 0);
    } catch (e) {
      throw ApiException('خطأ غير متوقع: $e', 0);
    }
  }

  /// طلب GET
  Future<http.Response> get(String endpoint, {Map<String, String>? headers, Map<String, dynamic>? queryParams, bool requireAuth = true}) {
    return _makeRequest('GET', endpoint, headers: headers, queryParams: queryParams, requireAuth: requireAuth);
  }

  /// طلب POST
  Future<http.Response> post(
    String endpoint,
    Map<String, dynamic>? body, {
    Map<String, String>? headers,
    bool requireAuth = true,
  }) {
    return _makeRequest('POST', endpoint,
        body: body, headers: headers, requireAuth: requireAuth);
  }

  /// طلب PUT
  Future<http.Response> put(
    String endpoint,
    Map<String, dynamic>? body, {
    Map<String, String>? headers,
  }) {
    return _makeRequest('PUT', endpoint, body: body, headers: headers);
  }

  /// طلب PATCH
  Future<http.Response> patch(
    String endpoint,
    Map<String, dynamic>? body, {
    Map<String, String>? headers,
  }) {
    return _makeRequest('PATCH', endpoint, body: body, headers: headers);
  }

  /// طلب DELETE
  Future<http.Response> delete(String endpoint, {Map<String, String>? headers}) {
    return _makeRequest('DELETE', endpoint, headers: headers);
  }

  /// معالجة استجابة API وإرجاع البيانات أو رمي استثناء
  T handleResponse<T>(
    http.Response response,
    T Function(dynamic) fromJson,
  ) {
    debugPrint('API Response Status: ${response.statusCode}');
    debugPrint('API Response Body: ${response.body}');

    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        final data = jsonDecode(response.body);
        return fromJson(data);
      } catch (e) {
        debugPrint('خطأ في تحليل البيانات: $e');
        throw ApiException('خطأ في تحليل البيانات من الخادم', response.statusCode);
      }
    } else {
      String errorMessage = _extractErrorMessage(response);
      debugPrint('API Error: $errorMessage (Status: ${response.statusCode})');
      throw ApiException(errorMessage, response.statusCode);
    }
  }

  /// استخراج رسالة الخطأ من استجابة الخادم
  String _extractErrorMessage(http.Response response) {
    try {
      final errorData = jsonDecode(response.body) as Map<String, dynamic>;

      // محاولة استخراج الرسالة من مختلف الحقول المحتملة
      if (errorData.containsKey('message') && errorData['message'] != null) {
        return errorData['message'].toString();
      }

      if (errorData.containsKey('Message') && errorData['Message'] != null) {
        return errorData['Message'].toString();
      }

      if (errorData.containsKey('error') && errorData['error'] != null) {
        return errorData['error'].toString();
      }

      if (errorData.containsKey('title') && errorData['title'] != null) {
        return errorData['title'].toString();
      }

      // إذا كانت الاستجابة تحتوي على كائن AuthResponse
      if (errorData.containsKey('success') && errorData['success'] == false) {
        return errorData['message']?.toString() ?? 'فشل في العملية';
      }

    } catch (e) {
      debugPrint('خطأ في تحليل رسالة الخطأ: $e');
    }

    // رسائل خطأ افتراضية حسب رمز الحالة
    switch (response.statusCode) {
      case 400:
        return 'بيانات غير صحيحة';
      case 401:
        return 'غير مصرح - يرجى المحاولة مرة أخرى';
      case 403:
        return 'غير مصرح لك بالوصول';
      case 404:
        return 'الصفحة المطلوبة غير موجودة';
      case 307:
        return 'خطأ في عنوان الخادم - يرجى التحقق من إعدادات الاتصال';
      case 409:
        return 'البيانات موجودة بالفعل';
      case 500:
        return 'خطأ في الخادم';
      default:
        return 'حدث خطأ غير متوقع';
    }
  }

  /// معالجة استجابة قائمة من API
  List<T> handleListResponse<T>(
    http.Response response,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      try {
        final responseBody = jsonDecode(response.body);

        // التحقق من نوع الاستجابة
        if (responseBody is List) {
          // إذا كانت الاستجابة قائمة مباشرة
          return responseBody.map((item) => fromJson(item as Map<String, dynamic>)).toList();
        } else if (responseBody is Map<String, dynamic>) {
          // إذا كانت الاستجابة كائن يحتوي على قائمة في خاصية 'data'
          if (responseBody.containsKey('data') && responseBody['data'] is List) {
            final data = responseBody['data'] as List<dynamic>;
            return data.map((item) => fromJson(item as Map<String, dynamic>)).toList();
          } else {
            // إذا كان الكائن لا يحتوي على 'data'، إرجاع قائمة فارغة
            return [];
          }
        } else {
          throw ApiException('تنسيق استجابة غير متوقع', response.statusCode);
        }
      } catch (e) {
        throw ApiException('خطأ في تحليل البيانات: $e', response.statusCode);
      }
    } else {
      String errorMessage = 'خطأ في الخادم';
      try {
        final errorData = jsonDecode(response.body) as Map<String, dynamic>;
        errorMessage = errorData['message'] ?? errorMessage;
      } catch (e) {
        // تجاهل خطأ تحليل رسالة الخطأ
      }
      throw ApiException(errorMessage, response.statusCode);
    }
  }

  /// حفظ بيانات المصادقة من AuthResponse
  Future<void> saveAuthResponse(AuthResponse authResponse) async {
    await _saveTokensToStorage(authResponse);
  }

  /// التحقق من حالة تسجيل الدخول
  bool get isLoggedIn => _accessToken != null &&
      (_tokenExpiresAt == null || DateTime.now().isBefore(_tokenExpiresAt!));

  /// تسجيل الخروج
  Future<void> logout() async {
    await clearTokens();
  }
}

/// استثناء API
class ApiException implements Exception {
  final String message;
  final int statusCode;

  const ApiException(this.message, this.statusCode);

  @override
  String toString() => 'ApiException: $message (Status: $statusCode)';

  /// التحقق من كون الخطأ خطأ مصادقة
  bool get isAuthError => statusCode == 401 || statusCode == 403;

  /// التحقق من كون الخطأ خطأ شبكة
  bool get isNetworkError => statusCode == 0;
}
