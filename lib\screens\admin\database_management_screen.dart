import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/database_management_controller.dart';
import '../../controllers/auth_controller.dart';
import '../../models/database_table_model.dart';
import '../../utils/responsive_helper.dart';
import '../widgets/common/loading_indicator.dart';
import '../widgets/common/error_view.dart';
import 'database_table_view.dart';
import 'database_management_enhancements.dart';

/// شاشة إدارة قاعدة البيانات
///
/// توفر واجهة شاملة لإدارة جداول قاعدة البيانات
class DatabaseManagementScreen extends StatefulWidget {
  const DatabaseManagementScreen({super.key});

  @override
  State<DatabaseManagementScreen> createState() => _DatabaseManagementScreenState();
}

class _DatabaseManagementScreenState extends State<DatabaseManagementScreen> {
  @override
  void initState() {
    super.initState();
    // تأكد من تحميل البيانات عند فتح الشاشة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final controller = Get.find<DatabaseManagementController>();

      // التحقق من حالة تسجيل الدخول أولاً
      final authController = Get.find<AuthController>();
      if (authController.isLoggedIn) {
        controller.refresh();
      } else {
        // إذا لم يكن المستخدم مسجل دخول، اعرض رسالة
        Get.snackbar(
          'تسجيل الدخول مطلوب',
          'يرجى تسجيل الدخول أولاً للوصول إلى إدارة قاعدة البيانات',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange.shade100,
          colorText: Colors.orange.shade800,
          duration: const Duration(seconds: 3),
        );
      }
    });
  }



  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DatabaseManagementController>();
    final isDesktop = ResponsiveHelper.isDesktop(context);

    return Container(
      color: AppColors.background,
      child: Column(
        children: [
          _buildHeader(controller),

          // إحصائيات قاعدة البيانات (للشاشات الكبيرة فقط)
          if (ResponsiveHelper.isDesktop(context))
            DatabaseManagementEnhancements.buildDatabaseStats(controller),

          Expanded(
            child: Obx(() {
              // عرض رسالة خطأ إذا كان هناك خطأ في الاتصال
              if (controller.error.isNotEmpty && controller.tables.isEmpty) {
                return Center(
                  child: ErrorView(
                    message: controller.error,
                    onRetry: () => controller.refresh(),
                  ),
                );
              }

              // عرض مؤشر التحميل إذا كان يتم تحميل الجداول
              if (controller.isLoadingTables && controller.tables.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('جاري تحميل الجداول...'),
                    ],
                  ),
                );
              }

              return isDesktop
                  ? _buildDesktopLayout(controller)
                  : _buildMobileLayout(controller);
            }),
          ),
        ],
      ),
    );
  }

  /// بناء رأس الشاشة
  Widget _buildHeader(DatabaseManagementController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.storage,
            size: 32,
            color: AppColors.primary,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'إدارة قاعدة البيانات',
                  style: AppStyles.headlineMedium.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Obx(() => Text(
                  controller.isLoading 
                      ? 'جاري التحميل...'
                      : 'إدارة وعرض جداول قاعدة البيانات',
                  style: AppStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                )),
              ],
            ),
          ),
          _buildConnectionStatus(controller),
          const SizedBox(width: 16),
          _buildRefreshButton(controller),
        ],
      ),
    );
  }

  /// بناء حالة الاتصال
  Widget _buildConnectionStatus(DatabaseManagementController controller) {
    return Obx(() {
      final isConnected = controller.databaseInfo.isNotEmpty;
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isConnected ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isConnected ? Colors.green : Colors.red,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isConnected ? Icons.check_circle : Icons.error,
              size: 16,
              color: isConnected ? Colors.green : Colors.red,
            ),
            const SizedBox(width: 6),
            Text(
              isConnected ? 'متصل' : 'غير متصل',
              style: AppStyles.bodySmall.copyWith(
                color: isConnected ? Colors.green : Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );
    });
  }

  /// بناء زر التحديث
  Widget _buildRefreshButton(DatabaseManagementController controller) {
    return Obx(() => IconButton(
      onPressed: controller.isLoading ? null : () => controller.refresh(),
      icon: controller.isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : const Icon(Icons.refresh),
      tooltip: 'تحديث البيانات',
    ));
  }

  /// بناء تخطيط سطح المكتب المحسن
  Widget _buildDesktopLayout(DatabaseManagementController controller) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // تحديد عرض قائمة الجداول بناءً على حجم الشاشة
        double sidebarWidth = 300;
        if (constraints.maxWidth > 1400) {
          sidebarWidth = 350; // شاشات كبيرة جداً
        } else if (constraints.maxWidth < 1000) {
          sidebarWidth = 250; // شاشات متوسطة
        }

        return Row(
          children: [
            // قائمة الجداول مع عرض متجاوب
            SizedBox(
              width: sidebarWidth,
              child: _buildTablesList(controller),
            ),
            // خط فاصل مع تحسينات بصرية
            Container(
              width: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppColors.border.withValues(alpha: 0.3),
                    AppColors.border,
                    AppColors.border.withValues(alpha: 0.3),
                  ],
                ),
              ),
            ),
            // عرض الجدول مع مساحة محسنة
            Expanded(
              child: _buildTableView(controller),
            ),
          ],
        );
      },
    );
  }

  /// بناء تخطيط الهاتف المحمول المحسن
  Widget _buildMobileLayout(DatabaseManagementController controller) {
    return Obx(() {
      if (controller.selectedTable == null) {
        return _buildMobileTablesList(controller);
      } else {
        return _buildMobileTableView(controller);
      }
    });
  }

  /// بناء قائمة الجداول للهاتف المحمول
  Widget _buildMobileTablesList(DatabaseManagementController controller) {
    return Column(
      children: [
        // رأس محسن للهاتف المحمول
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            border: Border(
              bottom: BorderSide(color: AppColors.border),
            ),
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Icon(Icons.table_chart, color: AppColors.primary),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'الجداول المتاحة',
                      style: AppStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                  Obx(() => Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${controller.filteredTables.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  )),
                ],
              ),
              const SizedBox(height: 12),
              // شريط البحث مبسط للهاتف
              TextField(
                decoration: InputDecoration(
                  hintText: 'البحث...',
                  prefixIcon: const Icon(Icons.search, size: 20),
                  suffixIcon: Obx(() {
                    return controller.tableSearchQuery.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear, size: 18),
                          onPressed: () => controller.clearTableSearch(),
                        )
                      : const SizedBox.shrink();
                  }),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  isDense: true,
                ),
                onChanged: (value) => controller.searchTables(value),
              ),
            ],
          ),
        ),
        // قائمة الجداول
        Expanded(
          child: _buildTablesListContent(controller),
        ),
      ],
    );
  }

  /// بناء عرض الجدول للهاتف المحمول
  Widget _buildMobileTableView(DatabaseManagementController controller) {
    return Column(
      children: [
        // شريط علوي للعودة
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            border: Border(
              bottom: BorderSide(color: AppColors.border),
            ),
          ),
          child: Row(
            children: [
              IconButton(
                onPressed: () => controller.clearSelection(),
                icon: const Icon(Icons.arrow_back),
                tooltip: 'العودة للجداول',
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      controller.selectedTable?.displayName ?? '',
                      style: AppStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Obx(() => Text(
                      '${controller.totalRecords} سجل',
                      style: AppStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    )),
                  ],
                ),
              ),
            ],
          ),
        ),
        // محتوى الجدول
        Expanded(
          child: _buildTableView(controller),
        ),
      ],
    );
  }

  /// بناء قائمة الجداول
  Widget _buildTablesList(DatabaseManagementController controller) {
    return Container(
      color: AppColors.surface,
      child: Column(
        children: [
          // رأس قائمة الجداول
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: AppColors.border),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.table_chart,
                  color: AppColors.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'الجداول المتاحة',
                  style: AppStyles.titleMedium.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Obx(() => Text(
                  '${controller.filteredTables.length}/${controller.tables.length}',
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                )),
              ],
            ),
          ),

          // شريط البحث في الجداول
          _buildTablesSearchBar(controller),
          // قائمة الجداول
          Expanded(
            child: _buildTablesListContent(controller),
          ),
        ],
      ),
    );
  }

  /// بناء محتوى قائمة الجداول
  Widget _buildTablesListContent(DatabaseManagementController controller) {
    return Obx(() {
      if (controller.isLoadingTables) {
        return const Center(child: LoadingIndicator());
      }

      if (controller.tables.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.table_chart_outlined,
                size: 64,
                color: AppColors.textSecondary,
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد جداول متاحة',
                style: AppStyles.bodyLarge.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'تحقق من الاتصال بقاعدة البيانات',
                style: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textHint,
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () => controller.refresh(),
                icon: const Icon(Icons.refresh),
                label: const Text('إعادة المحاولة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        );
      }

      final tables = controller.filteredTables;

      if (tables.isEmpty && controller.tableSearchQuery.isNotEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search_off,
                size: 64,
                color: AppColors.textSecondary,
              ),
              const SizedBox(height: 16),
              Text(
                'لم يتم العثور على جداول',
                style: AppStyles.bodyLarge.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'للبحث: "${controller.tableSearchQuery}"',
                style: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textHint,
                ),
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        itemCount: tables.length,
        itemBuilder: (context, index) {
          final table = tables[index];
          return _buildTableListItem(controller, table);
        },
      );
    });
  }

  /// بناء شريط البحث في الجداول
  Widget _buildTablesSearchBar(DatabaseManagementController controller) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.background,
        border: Border(
          bottom: BorderSide(color: AppColors.border),
        ),
      ),
      child: Obx(() => TextField(
        decoration: InputDecoration(
          hintText: 'البحث في الجداول...',
          prefixIcon: const Icon(Icons.search, size: 20),
          suffixIcon: controller.tableSearchQuery.isNotEmpty
            ? IconButton(
                icon: const Icon(Icons.clear, size: 20),
                onPressed: () => controller.clearTableSearch(),
              )
            : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: AppColors.border),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
          isDense: true,
        ),
        onChanged: (value) => controller.searchTables(value),
      )),
    );
  }

  /// بناء عنصر في قائمة الجداول
  Widget _buildTableListItem(DatabaseManagementController controller, DatabaseTable table) {
    return Obx(() {
      final isSelected = controller.selectedTable?.name == table.name;

      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : null,
          borderRadius: BorderRadius.circular(8),
          border: isSelected ? Border.all(color: AppColors.primary) : null,
        ),
        child: ListTile(
          leading: Text(
            // table.icon,
            style: TextStyle(
              fontSize: 20,
              color: isSelected ? AppColors.primary : AppColors.textSecondary,
            ),
          ),
          title: Text(
            table.displayName,
            style: AppStyles.bodyMedium.copyWith(
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              color: isSelected ? AppColors.primary : AppColors.textPrimary,
            ),
          ),
          subtitle: Text(
            '${table.columns.length} عمود • ${table.recordCount} سجل',
            style: AppStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          onTap: () => controller.selectTable(table),
        ),
      );
    });
  }

  /// بناء عرض الجدول
  Widget _buildTableView(DatabaseManagementController controller) {
    return Obx(() {
      if (controller.selectedTable == null) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.table_chart_outlined,
                size: 64,
                color: AppColors.textSecondary,
              ),
              const SizedBox(height: 16),
              Text(
                'اختر جدولاً لعرض بياناته',
                style: AppStyles.bodyLarge.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        );
      }

      if (controller.error.isNotEmpty) {
        return Center(
          child: ErrorView(
            message: controller.error,
            onRetry: () => controller.loadTableData(),
          ),
        );
      }

      return Column(
        children: [
          // إحصائيات الجدول
          DatabaseManagementEnhancements.buildTableStats(
            controller.selectedTable!,
            controller,
          ),

          // عرض الجدول
          Expanded(
            child: DatabaseTableView(
              controller: controller,
              table: controller.selectedTable!,
            ),
          ),

          // شريط pagination متقدم
          DatabaseManagementEnhancements.buildAdvancedPagination(controller),
        ],
      );
    });
  }
}