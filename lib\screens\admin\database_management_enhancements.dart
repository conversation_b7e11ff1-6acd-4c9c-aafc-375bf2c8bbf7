import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../controllers/database_management_controller.dart';
import '../../models/database_table_model.dart';

/// تحسينات إضافية لتبويب إدارة قاعدة البيانات
class DatabaseManagementEnhancements {

  /// بناء شريط أدوات متقدم
  static Widget buildAdvancedToolbar({
    required DatabaseManagementController controller,
    required VoidCallback onRefresh,
    required VoidCallback onSettings,
    required VoidCallback onHelp,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.surface,
        border: Border(
          bottom: BorderSide(color: AppColors.border),
        ),
      ),
      child: Row(
        children: [
          // أزرار الإجراءات السريعة
          _buildQuickActionButton(
            icon: Icons.refresh,
            label: 'تحديث',
            onPressed: onRefresh,
            color: Colors.blue,
          ),
          const SizedBox(width: 8),
          _buildQuickActionButton(
            icon: Icons.settings,
            label: 'إعدادات',
            onPressed: onSettings,
            color: Colors.grey,
          ),
          const SizedBox(width: 8),
          _buildQuickActionButton(
            icon: Icons.help_outline,
            label: 'مساعدة',
            onPressed: onHelp,
            color: Colors.orange,
          ),

          const Spacer(),

          // معلومات سريعة
          Obx(() => _buildQuickInfo(controller)),
        ],
      ),
    );
  }

  /// بناء زر إجراء سريع
  static Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required Color color,
  }) {
    return Tooltip(
      message: label,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: color.withValues(alpha: 0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء معلومات سريعة
  static Widget _buildQuickInfo(DatabaseManagementController controller) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.info_outline,
            size: 14,
            color: AppColors.primary,
          ),
          const SizedBox(width: 4),
          Text(
            '${controller.tables.length} جدول • ${controller.totalRecords} سجل',
            style: TextStyle(
              fontSize: 12,
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
  
  /// بناء مؤشر حالة الاتصال المحسن
  static Widget buildEnhancedConnectionStatus(DatabaseManagementController controller) {
    return Obx(() {
      final isConnected = controller.databaseInfo.isNotEmpty;
      final isLoading = controller.isLoading;
      
      if (isLoading) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.blue, width: 1),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
              ),
              const SizedBox(width: 6),
              Text(
                'جاري الاتصال...',
                style: AppStyles.bodySmall.copyWith(
                  color: Colors.blue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        );
      }
      
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isConnected ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isConnected ? Colors.green : Colors.red,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isConnected ? Icons.check_circle : Icons.error,
              size: 16,
              color: isConnected ? Colors.green : Colors.red,
            ),
            const SizedBox(width: 6),
            Text(
              isConnected ? 'متصل' : 'غير متصل',
              style: AppStyles.bodySmall.copyWith(
                color: isConnected ? Colors.green : Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );
    });
  }

  /// بناء إحصائيات قاعدة البيانات المتجاوبة (مصغرة)
  static Widget buildDatabaseStats(DatabaseManagementController controller) {
    return Obx(() {
      final tables = controller.tables;
      final totalTables = tables.length;
      final totalRecords = tables.fold<int>(0, (sum, table) => sum + table.recordCount);

      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppColors.border.withValues(alpha: 0.5)),
        ),
        child: Row(
          children: [
            Icon(Icons.analytics, color: AppColors.primary, size: 16),
            const SizedBox(width: 6),
            Text(
              'إحصائيات قاعدة البيانات',
              style: AppStyles.bodySmall.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildCompactStatItem(
                    icon: Icons.table_chart,
                    value: totalTables.toString(),
                    label: 'جدول',
                    color: Colors.blue,
                  ),
                  _buildCompactStatItem(
                    icon: Icons.storage,
                    value: totalRecords.toString(),
                    label: 'سجل',
                    color: Colors.green,
                  ),
                  _buildCompactStatItem(
                    icon: Icons.memory,
                    value: controller.databaseInfo.isNotEmpty ? 'نشط' : 'غير نشط',
                    label: '',
                    color: controller.databaseInfo.isNotEmpty ? Colors.green : Colors.red,
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  /// بناء عنصر إحصائية مصغر
  static Widget _buildCompactStatItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 14),
        const SizedBox(width: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
        if (label.isNotEmpty) ...[
          const SizedBox(width: 2),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ],
    );
  }

  /// بناء شريط البحث المحسن والمتقدم
  static Widget buildAdvancedSearchBar({
    required TextEditingController searchController,
    required DatabaseManagementController controller,
    required List<DatabaseColumn> searchableColumns,
    required Function(String) onSearch,
    required VoidCallback onClear,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
        boxShadow: [
          BoxShadow(
            color: AppColors.getShadowColor(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // رأس البحث
          Row(
            children: [
              Icon(Icons.search, color: AppColors.primary),
              const SizedBox(width: 8),
              Text(
                'البحث المتقدم في البيانات',
                style: AppStyles.titleSmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
              const Spacer(),
              // زر البحث المحفوظ
              IconButton(
                onPressed: () => _showSavedSearches(controller),
                icon: const Icon(Icons.bookmark_outline),
                tooltip: 'البحث المحفوظ',
              ),
            ],
          ),
          const SizedBox(height: 12),

          // شريط البحث الرئيسي
          Row(
            children: [
              // حقل البحث المحسن
              Expanded(
                flex: 4,
                child: TextField(
                  controller: searchController,
                  decoration: InputDecoration(
                    hintText: 'ابحث في جميع الحقول...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (searchController.text.isNotEmpty)
                          IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: onClear,
                            tooltip: 'مسح البحث',
                          ),
                        IconButton(
                          icon: const Icon(Icons.tune),
                          onPressed: () => _showAdvancedFilters(controller),
                          tooltip: 'فلاتر متقدمة',
                        ),
                      ],
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  onSubmitted: onSearch,
                  onChanged: (value) {
                    // البحث التلقائي أثناء الكتابة (مع تأخير)
                    if (value.isEmpty) {
                      onClear();
                    }
                  },
                ),
              ),
              const SizedBox(width: 12),

              // أزرار الإجراءات
              ElevatedButton.icon(
                onPressed: () => onSearch(searchController.text),
                icon: const Icon(Icons.search),
                label: const Text('بحث'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // زر مسح الفلاتر
              Obx(() => controller.searchQuery.isNotEmpty || controller.filters.isNotEmpty
                ? OutlinedButton.icon(
                    onPressed: () {
                      onClear();
                      controller.clearFilters();
                    },
                    icon: const Icon(Icons.filter_alt_off),
                    label: const Text('مسح'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.orange,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                  )
                : const SizedBox.shrink()),
            ],
          ),

          // شريط الفلاتر النشطة
          Obx(() => _buildActiveFilters(controller)),

          // إحصائيات البحث
          Obx(() => _buildSearchStats(controller)),
        ],
      ),
    );
  }

  /// بناء الفلاتر النشطة
  static Widget _buildActiveFilters(DatabaseManagementController controller) {
    final hasFilters = controller.searchQuery.isNotEmpty || controller.filters.isNotEmpty;

    if (!hasFilters) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(top: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الفلاتر النشطة:',
            style: AppStyles.bodySmall.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: [
              // فلتر البحث
              if (controller.searchQuery.isNotEmpty)
                _buildFilterChip(
                  label: 'البحث: "${controller.searchQuery}"',
                  onRemove: () => controller.clearSearch(),
                ),

              // فلاتر أخرى
              ...controller.filters.entries.map((entry) =>
                _buildFilterChip(
                  label: '${entry.key}: ${entry.value}',
                  onRemove: () {
                    final newFilters = Map<String, dynamic>.from(controller.filters);
                    newFilters.remove(entry.key);
                    controller.applyFilters(newFilters);
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء chip للفلتر
  static Widget _buildFilterChip({
    required String label,
    required VoidCallback onRemove,
  }) {
    return Chip(
      label: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
      deleteIcon: const Icon(Icons.close, size: 16),
      onDeleted: onRemove,
      backgroundColor: AppColors.primary.withValues(alpha: 0.1),
      deleteIconColor: AppColors.primary,
      side: BorderSide(color: AppColors.primary.withValues(alpha: 0.3)),
    );
  }

  /// بناء إحصائيات البحث
  static Widget _buildSearchStats(DatabaseManagementController controller) {
    if (controller.searchQuery.isEmpty && controller.filters.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.info_outline, size: 16, color: Colors.blue),
          const SizedBox(width: 6),
          Text(
            'تم العثور على ${controller.tableData.length} نتيجة',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.blue,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// عرض البحث المحفوظ
  static void _showSavedSearches(DatabaseManagementController controller) {
    // TODO: تطبيق البحث المحفوظ
    Get.snackbar(
      'قريباً',
      'ميزة البحث المحفوظ ستكون متاحة قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// عرض الفلاتر المتقدمة
  static void _showAdvancedFilters(DatabaseManagementController controller) {
    // TODO: تطبيق الفلاتر المتقدمة
    Get.snackbar(
      'قريباً',
      'ميزة الفلاتر المتقدمة ستكون متاحة قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// بناء مؤشر التحميل المحسن
  static Widget buildEnhancedLoadingIndicator({
    required String message,
    bool showProgress = false,
    double? progress,
  }) {
    return Center(
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppColors.getShadowColor(0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showProgress && progress != null)
              CircularProgressIndicator(
                value: progress,
                backgroundColor: AppColors.border,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              )
            else
              CircularProgressIndicator(
                backgroundColor: AppColors.border,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
              ),
            const SizedBox(height: 16),
            Text(
              message,
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            if (showProgress && progress != null) ...[
              const SizedBox(height: 8),
              Text(
                '${(progress * 100).toInt()}%',
                style: AppStyles.bodySmall.copyWith(
                  color: AppColors.textHint,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء شريط pagination متقدم ومحسن
  static Widget buildAdvancedPagination(DatabaseManagementController controller) {
    return Obx(() {
      if (controller.tableData.isEmpty && controller.searchQuery.isEmpty) {
        return const SizedBox.shrink();
      }

      final currentPage = controller.currentPage;
      final totalPages = controller.totalPages;
      final pageSize = controller.pageSize;
      final totalRecords = controller.totalRecords;
      final startRecord = totalRecords > 0 ? ((currentPage - 1) * pageSize) + 1 : 0;
      final endRecord = (currentPage * pageSize).clamp(0, totalRecords);

      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.surface,
          border: Border(
            top: BorderSide(color: AppColors.border),
          ),
          boxShadow: [
            BoxShadow(
              color: AppColors.getShadowColor(0.05),
              blurRadius: 8,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          children: [
            // الصف الأول: معلومات مفصلة
            Row(
              children: [
                // معلومات السجلات
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 16,
                        color: AppColors.primary,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        totalRecords > 0
                          ? 'عرض $startRecord-$endRecord من أصل $totalRecords سجل'
                          : 'لا توجد سجلات',
                        style: TextStyle(
                          fontSize: 13,
                          color: AppColors.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),

                const Spacer(),

                // معلومات الصفحة
                if (totalPages > 0)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.grey.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      'الصفحة $currentPage من $totalPages',
                      style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),

            if (totalPages > 1) ...[
              const SizedBox(height: 12),

              // الصف الثاني: أدوات التنقل
              Row(
                children: [
                  // اختيار حجم الصفحة
                  Row(
                    children: [
                      const Text('عرض: '),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppColors.border),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: DropdownButton<int>(
                          value: pageSize,
                          underline: const SizedBox.shrink(),
                          items: [10, 25, 50, 100, 200].map((size) {
                            return DropdownMenuItem<int>(
                              value: size,
                              child: Text('$size'),
                            );
                          }).toList(),
                          onChanged: (newSize) {
                            if (newSize != null) {
                              controller.changePageSize(newSize);
                            }
                          },
                        ),
                      ),
                      const Text(' سجل'),
                    ],
                  ),

                  const Spacer(),

                  // أزرار التنقل المحسنة
                  Row(
                    children: [
                      // الذهاب للصفحة الأولى
                      _buildPaginationButton(
                        icon: Icons.first_page,
                        tooltip: 'الصفحة الأولى',
                        enabled: currentPage > 1,
                        onPressed: () => controller.goToPage(1),
                      ),

                      // الصفحة السابقة
                      _buildPaginationButton(
                        icon: Icons.chevron_left,
                        tooltip: 'الصفحة السابقة',
                        enabled: currentPage > 1,
                        onPressed: () => controller.previousPage(),
                      ),

                      const SizedBox(width: 8),

                      // أرقام الصفحات الذكية
                      ..._buildSmartPageNumbers(currentPage, totalPages, controller),

                      const SizedBox(width: 8),

                      // الصفحة التالية
                      _buildPaginationButton(
                        icon: Icons.chevron_right,
                        tooltip: 'الصفحة التالية',
                        enabled: currentPage < totalPages,
                        onPressed: () => controller.nextPage(),
                      ),

                      // الذهاب للصفحة الأخيرة
                      _buildPaginationButton(
                        icon: Icons.last_page,
                        tooltip: 'الصفحة الأخيرة',
                        enabled: currentPage < totalPages,
                        onPressed: () => controller.goToPage(totalPages),
                      ),

                      const SizedBox(width: 16),

                      // الذهاب لصفحة محددة
                      _buildGoToPageButton(controller, totalPages),
                    ],
                  ),
                ],
              ),
            ],
          ],
        ),
      );
    });
  }

  /// بناء زر pagination
  static Widget _buildPaginationButton({
    required IconData icon,
    required String tooltip,
    required bool enabled,
    required VoidCallback onPressed,
  }) {
    return Tooltip(
      message: tooltip,
      child: IconButton(
        onPressed: enabled ? onPressed : null,
        icon: Icon(icon),
        style: IconButton.styleFrom(
          backgroundColor: enabled
            ? AppColors.primary.withValues(alpha: 0.1)
            : Colors.grey.withValues(alpha: 0.1),
          foregroundColor: enabled ? AppColors.primary : Colors.grey,
        ),
      ),
    );
  }

  /// بناء أرقام الصفحات الذكية
  static List<Widget> _buildSmartPageNumbers(
    int currentPage,
    int totalPages,
    DatabaseManagementController controller
  ) {
    List<Widget> pageButtons = [];

    // حساب النطاق المعروض
    int start = 1;
    int end = totalPages;

    if (totalPages > 7) {
      if (currentPage <= 4) {
        end = 7;
      } else if (currentPage >= totalPages - 3) {
        start = totalPages - 6;
      } else {
        start = currentPage - 3;
        end = currentPage + 3;
      }
    }

    // إضافة الصفحة الأولى إذا لم تكن في النطاق
    if (start > 1) {
      pageButtons.add(_buildPageButton(1, currentPage, controller));
      if (start > 2) {
        pageButtons.add(const Padding(
          padding: EdgeInsets.symmetric(horizontal: 4),
          child: Text('...', style: TextStyle(color: Colors.grey)),
        ));
      }
    }

    // إضافة الصفحات في النطاق
    for (int i = start; i <= end; i++) {
      pageButtons.add(_buildPageButton(i, currentPage, controller));
    }

    // إضافة الصفحة الأخيرة إذا لم تكن في النطاق
    if (end < totalPages) {
      if (end < totalPages - 1) {
        pageButtons.add(const Padding(
          padding: EdgeInsets.symmetric(horizontal: 4),
          child: Text('...', style: TextStyle(color: Colors.grey)),
        ));
      }
      pageButtons.add(_buildPageButton(totalPages, currentPage, controller));
    }

    return pageButtons;
  }

  /// بناء زر رقم الصفحة
  static Widget _buildPageButton(
    int pageNumber,
    int currentPage,
    DatabaseManagementController controller
  ) {
    final isActive = pageNumber == currentPage;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 2),
      child: TextButton(
        onPressed: () => controller.goToPage(pageNumber),
        style: TextButton.styleFrom(
          backgroundColor: isActive
              ? AppColors.primary
              : Colors.transparent,
          foregroundColor: isActive
              ? Colors.white
              : AppColors.textPrimary,
          minimumSize: const Size(40, 40),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: isActive
              ? BorderSide.none
              : BorderSide(color: AppColors.border),
          ),
        ),
        child: Text(
          pageNumber.toString(),
          style: TextStyle(
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  /// بناء زر الذهاب لصفحة محددة
  static Widget _buildGoToPageButton(
    DatabaseManagementController controller,
    int totalPages
  ) {
    return Tooltip(
      message: 'الذهاب لصفحة محددة',
      child: IconButton(
        onPressed: () => _showGoToPageDialog(controller, totalPages),
        icon: const Icon(Icons.more_horiz),
        style: IconButton.styleFrom(
          backgroundColor: Colors.grey.withValues(alpha: 0.1),
        ),
      ),
    );
  }

  /// عرض حوار الذهاب لصفحة محددة
  static void _showGoToPageDialog(
    DatabaseManagementController controller,
    int totalPages
  ) {
    final textController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('الذهاب إلى صفحة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: textController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                labelText: 'رقم الصفحة (1-$totalPages)',
                border: const OutlineInputBorder(),
              ),
              autofocus: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final pageNumber = int.tryParse(textController.text);
              if (pageNumber != null && pageNumber >= 1 && pageNumber <= totalPages) {
                controller.goToPage(pageNumber);
                Get.back();
              } else {
                Get.snackbar(
                  'خطأ',
                  'يرجى إدخال رقم صفحة صحيح بين 1 و $totalPages',
                  snackPosition: SnackPosition.BOTTOM,
                );
              }
            },
            child: const Text('ذهاب'),
          ),
        ],
      ),
    );
  }

  /// بناء شريط إحصائيات الجدول (مصغر)
  static Widget buildTableStats(DatabaseTable table, DatabaseManagementController controller) {
    return Obx(() {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: AppColors.surface,
          border: Border(
            bottom: BorderSide(color: AppColors.border.withValues(alpha: 0.5)),
          ),
        ),
        child: Row(
          children: [
            // أيقونة الجدول مصغرة
            Icon(
              Icons.table_chart,
              color: AppColors.primary,
              size: 16,
            ),
            const SizedBox(width: 8),

            // اسم الجدول
            Text(
              table.displayName,
              style: AppStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ),

            const SizedBox(width: 12),

            // الإحصائيات المصغرة
            Expanded(
              child: Row(
                children: [
                  _buildStatChip(
                    icon: Icons.view_column,
                    label: '${table.columns.length} عمود',
                    color: Colors.blue,
                  ),
                  const SizedBox(width: 6),
                  _buildStatChip(
                    icon: Icons.table_rows,
                    label: '${controller.totalRecords} سجل',
                    color: Colors.green,
                  ),
                  if (controller.searchQuery.isNotEmpty) ...[
                    const SizedBox(width: 6),
                    _buildStatChip(
                      icon: Icons.search,
                      label: 'مفلتر',
                      color: Colors.orange,
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  /// بناء chip للإحصائيات
  static Widget _buildStatChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
